// FIXED JavaScript code for Frappe Desk Script Report
// Copy this into the Client Script section of your Frappe Desk Script Report

frappe.query_reports["Register Salary 1"] = {
    filters: [
        {
            fieldname: "from_date",
            label: __("From"),
            fieldtype: "Date",
            default: frappe.datetime.add_months(frappe.datetime.get_today(), -1),
            reqd: 1,
            width: "100px",
        },
        {
            fieldname: "to_date",
            label: __("To"),
            fieldtype: "Date",
            default: frappe.datetime.get_today(),
            reqd: 1,
            width: "100px",
        },
        {
            fieldname: "currency",
            fieldtype: "Link",
            options: "Currency",
            label: __("Currency"),
            width: "50px",
        },
        {
            fieldname: "employee",
            label: __("Employee"),
            fieldtype: "Link",
            options: "Employee",
            width: "100px",
        },
        {
            fieldname: "company",
            label: __("Company"),
            fieldtype: "Link",
            options: "Company",
            default: frappe.defaults.get_user_default("Company"),
            width: "100px",
            reqd: 1,
        },
        {
            fieldname: "docstatus",
            label: __("Document Status"),
            fieldtype: "Select",
            options: ["Draft", "Submitted", "Cancelled"],
            default: "Draft",
            width: "100px",
        },
        {
            fieldname: "department",
            label: __("Department"),
            fieldtype: "Link",
            options: "Department",
            width: "100px",
            get_query: function () {
                return {
                    filters: {
                        company: frappe.query_report.get_filter_value("company"),
                    },
                };
            },
        },
        {
            fieldname: "designation",
            label: __("Designation"),
            fieldtype: "Link",
            options: "Designation",
            width: "100px",
        },
        {
            fieldname: "branch",
            label: __("Branch"),
            fieldtype: "Link",
            options: "Branch",
            width: "100px",
        },
    ],

    onload: function(report) {
        // Check if workflow exists and add buttons
        frappe.call({
            method: "frappe.client.get_value",
            args: {
                doctype: "Workflow",
                filters: {"document_type": "Salary Slip", "is_active": 1},
                fieldname: "name"
            },
            callback: function(r) {
                report.has_workflow = r.message && r.message.name;
                setup_approval_buttons(report);
            }
        });
    },

    get_datatable_options: function(options) {
        return Object.assign(options, {
            checkboxColumn: true,
            events: {
                onCheckRow: function() {
                    // Handle row selection
                }
            }
        });
    }
};

function setup_approval_buttons(report) {
    // Add workflow-based buttons in separate group
    report.page.add_inner_button(__("Approve Selected"), function() {
        approve_selected_salary_slips(report);
    }, __("Workflow Actions"));

    report.page.add_inner_button(__("Approve All Draft"), function() {
        approve_all_salary_slips(report);
    }, __("Workflow Actions"));

    if (report.has_workflow) {
        report.page.add_inner_button(__("Reject Selected"), function() {
            reject_selected_salary_slips(report);
        }, __("Workflow Actions"));

        // Add workflow info button
        report.page.add_inner_button(__("Workflow Info"), function() {
            show_workflow_info(report);
        }, __("Workflow Actions"));
    }

    // Add utility buttons in separate group
    report.page.add_inner_button(__("Refresh"), function() {
        report.refresh();
    }, __("Report Actions"));

    // Add export button
    report.page.add_inner_button(__("Export Data"), function() {
        export_salary_data(report);
    }, __("Report Actions"));
}

function approve_selected_salary_slips(report) {
    let selected_rows = report.datatable.rowmanager.getCheckedRows();
    if (selected_rows.length === 0) {
        frappe.msgprint(__("Please select salary slips to approve"));
        return;
    }
    
    let actionable_slips = selected_rows.filter(row => {
        let slip_data = report.data[row];
        return slip_data.docstatus === 0;
    }).map(row => report.data[row].salary_slip_id);
    
    if (actionable_slips.length === 0) {
        frappe.msgprint(__("No draft salary slips selected"));
        return;
    }

    frappe.confirm(
        __("Are you sure you want to submit {0} salary slip(s)?", [actionable_slips.length]),
        function() {
            process_salary_slips(actionable_slips, "approve", report);
        }
    );
}

function approve_all_salary_slips(report) {
    if (!report.data || report.data.length === 0) {
        frappe.msgprint(__("No salary slips to approve"));
        return;
    }

    let salary_slip_ids = report.data.filter(row => {
        return row.docstatus === 0;
    }).map(row => row.salary_slip_id);

    if (salary_slip_ids.length === 0) {
        frappe.msgprint(__("No draft salary slips found"));
        return;
    }

    frappe.confirm(
        __("Are you sure you want to submit all {0} draft salary slip(s)?", [salary_slip_ids.length]),
        function() {
            process_salary_slips(salary_slip_ids, "approve", report);
        }
    );
}

function reject_selected_salary_slips(report) {
    let selected_rows = report.datatable.rowmanager.getCheckedRows();
    if (selected_rows.length === 0) {
        frappe.msgprint(__("Please select salary slips to reject"));
        return;
    }

    let rejectable_slips = selected_rows.filter(row => {
        let slip_data = report.data[row];
        return slip_data.docstatus === 0;
    }).map(row => report.data[row].salary_slip_id);
    
    if (rejectable_slips.length === 0) {
        frappe.msgprint(__("No draft salary slips selected"));
        return;
    }

    frappe.prompt([
        {
            fieldname: 'reason',
            fieldtype: 'Small Text',
            label: __('Reason for Rejection'),
            reqd: 1
        }
    ], function(values) {
        frappe.msgprint(__("Rejection noted for {0} salary slip(s). Reason: {1}", [rejectable_slips.length, values.reason]));
        // For rejection in Frappe Desk, you might want to add comments to the documents
        // This is a simplified version - actual rejection would need server-side processing
    }, __("Reject Salary Slips"));
}

function process_salary_slips(salary_slip_ids, action, report) {
    let processed = 0;
    let errors = 0;
    let total = salary_slip_ids.length;
    
    frappe.show_progress(__("Processing Salary Slips"), 0, total, __("Starting..."));
    
    // Process each salary slip one by one
    let process_next = function(index) {
        if (index >= total) {
            frappe.hide_progress();
            frappe.msgprint(__("{0} salary slip(s) submitted successfully, {1} failed", [processed, errors]));
            report.refresh();
            return;
        }
        
        let slip_id = salary_slip_ids[index];
        
        frappe.call({
            method: "frappe.client.submit_doc",
            args: {
                doc: {
                    doctype: "Salary Slip",
                    name: slip_id
                }
            },
            callback: function(r) {
                if (r.message) {
                    processed++;
                } else {
                    errors++;
                }
                
                frappe.show_progress(__("Processing Salary Slips"), index + 1, total, 
                    __("Processed {0} of {1}", [index + 1, total]));
                
                // Process next item
                setTimeout(function() {
                    process_next(index + 1);
                }, 100); // Small delay to prevent overwhelming the server
            },
            error: function() {
                errors++;
                frappe.show_progress(__("Processing Salary Slips"), index + 1, total, 
                    __("Processed {0} of {1}", [index + 1, total]));
                
                // Process next item even on error
                setTimeout(function() {
                    process_next(index + 1);
                }, 100);
            }
        });
    };
    
    // Start processing
    process_next(0);
}
