{"actions": [], "autoname": "ASSIGN.###", "creation": "2017-04-17 20:17:01.114822", "doctype": "DocType", "editable_grid": 1, "engine": "InnoDB", "field_order": ["cargo", "amount", "expected_loading_date", "column_break_4", "container_number", "units", "section_transporter_details", "transporter_type", "sub_contractor", "assigned_vehicle", "vehicle_plate_number", "assigned_trailer", "trailer_plate_number", "column_break_18", "assigned_driver", "driver_name", "passport_number", "driver_licence", "driver_contact", "section_route", "route", "vehicle_status", "vehicle_trip", "column_break_21", "status", "created_trip", "section_extra", "loading_date", "lodge_permit", "dispatch_date", "cargo_type", "section_vehicle_attachments", "attach_1", "attach_2", "attach_3", "attach_4", "attach_column", "description_1", "description_2", "description_3", "description_4", "section_file_reference", "file_number", "import", "column_break_43", "export", "parent", "parentfield", "parenttype"], "fields": [{"fieldname": "cargo", "fieldtype": "Link", "label": "Container", "options": "Cargo Details"}, {"fieldname": "amount", "fieldtype": "Float", "label": "Quantity"}, {"fieldname": "expected_loading_date", "fieldtype": "Date", "in_list_view": 1, "label": "Expected Loading Date"}, {"fieldname": "column_break_4", "fieldtype": "Column Break"}, {"fieldname": "container_number", "fieldtype": "Data", "in_global_search": 1, "in_list_view": 1, "in_standard_filter": 1, "label": "Container Number"}, {"fieldname": "units", "fieldtype": "Link", "label": "Units", "options": "Unit of Measure"}, {"fieldname": "section_transporter_details", "fieldtype": "Section Break", "label": "TRANPORT DETAILS"}, {"fieldname": "transporter_type", "fieldtype": "Select", "label": "Transporter", "options": "\nBravo\nSub-Contractor\nSelf Drive"}, {"fieldname": "sub_contractor", "fieldtype": "Link", "hidden": 1, "in_global_search": 1, "in_standard_filter": 1, "label": "Sub-Contractor", "options": "Supplier"}, {"fieldname": "assigned_vehicle", "fieldtype": "Link", "label": "Assigned Vehicle", "options": "Vehicle", "read_only": 1}, {"fieldname": "vehicle_plate_number", "fieldtype": "Data", "in_global_search": 1, "in_list_view": 1, "in_standard_filter": 1, "label": "Vehicle Plate Number", "read_only": 1}, {"fieldname": "assigned_trailer", "fieldtype": "Link", "label": "Assigned Trailer", "options": "Trailer", "read_only": 1}, {"fieldname": "trailer_plate_number", "fieldtype": "Data", "in_list_view": 1, "label": "Trailer Plate Number", "read_only": 1}, {"fieldname": "column_break_18", "fieldtype": "Column Break"}, {"fieldname": "assigned_driver", "fieldtype": "Link", "hidden": 1, "label": "Assigned Driver", "options": "Employee"}, {"fieldname": "driver_name", "fieldtype": "Data", "in_list_view": 1, "label": "Driver Name", "read_only": 1}, {"fieldname": "passport_number", "fieldtype": "Data", "in_list_view": 1, "label": "Driver Passport", "read_only": 1}, {"fieldname": "driver_licence", "fieldtype": "Data", "label": "Driver Licence", "read_only": 1}, {"fieldname": "driver_contact", "fieldtype": "Data", "label": "Driver Phone Number", "read_only": 1}, {"fieldname": "section_route", "fieldtype": "Section Break", "label": "Route Details"}, {"fieldname": "route", "fieldtype": "Link", "label": "Trip Route", "options": "Trip Route"}, {"fieldname": "vehicle_status", "fieldtype": "Int", "hidden": 1, "label": "Vehicle Status", "read_only": 1}, {"fieldname": "vehicle_trip", "fieldtype": "Link", "label": "Previous Trip", "options": "Vehicle Trip", "read_only": 1}, {"fieldname": "column_break_21", "fieldtype": "Column Break"}, {"default": "Not Processed", "fieldname": "status", "fieldtype": "Select", "in_standard_filter": 1, "label": "Status", "options": "\nNot Processed\nProcessed", "permlevel": 1}, {"fieldname": "created_trip", "fieldtype": "Link", "label": "Created Trip", "options": "Vehicle Trip", "read_only": 1}, {"fieldname": "section_extra", "fieldtype": "Section Break", "label": "Extra Info"}, {"fieldname": "loading_date", "fieldtype": "Date", "label": "Loading Date"}, {"fieldname": "lodge_permit", "fieldtype": "Date", "label": "Lodge Permit Date"}, {"fieldname": "dispatch_date", "fieldtype": "Date", "label": "Dispatch Date"}, {"fieldname": "cargo_type", "fieldtype": "Data", "label": "Cargo Type", "read_only": 1}, {"fieldname": "section_vehicle_attachments", "fieldtype": "Section Break", "label": "Vehicle Attachments"}, {"fieldname": "attach_1", "fieldtype": "Attach", "label": "Attachment 1"}, {"fieldname": "attach_2", "fieldtype": "Attach", "label": "Attachment 2"}, {"fieldname": "attach_3", "fieldtype": "Attach", "label": "Attachment 3"}, {"fieldname": "attach_4", "fieldtype": "Attach", "label": "Attachment 4"}, {"fieldname": "attach_column", "fieldtype": "Column Break"}, {"fieldname": "description_1", "fieldtype": "Data", "label": "Description 1"}, {"fieldname": "description_2", "fieldtype": "Data", "label": "Description 2"}, {"fieldname": "description_3", "fieldtype": "Data", "label": "Description 3"}, {"fieldname": "description_4", "fieldtype": "Data", "label": "Description 4"}, {"fieldname": "section_file_reference", "fieldtype": "Section Break"}, {"fieldname": "file_number", "fieldtype": "Link", "label": "File Number", "options": "Files", "read_only": 1}, {"fieldname": "import", "fieldtype": "Link", "label": "Import Reference", "options": "Import", "read_only": 1}, {"fieldname": "column_break_43", "fieldtype": "Column Break"}, {"fieldname": "export", "fieldtype": "Link", "label": "Export Reference", "options": "Export", "read_only": 1}, {"fieldname": "parent", "fieldtype": "Data", "hidden": 1, "label": "Parent", "no_copy": 1, "print_hide": 1, "read_only": 1}, {"fieldname": "parentfield", "fieldtype": "Data", "hidden": 1, "label": "Parent Field", "no_copy": 1, "print_hide": 1, "read_only": 1}, {"fieldname": "parenttype", "fieldtype": "Data", "hidden": 1, "label": "Parent Type", "no_copy": 1, "print_hide": 1, "read_only": 1}], "istable": 1, "links": [], "modified": "2025-02-10 14:33:59.142718", "modified_by": "Administrator", "module": "Fleet Management", "name": "Transport Assignment", "owner": "Administrator", "permissions": [], "sort_field": "modified", "sort_order": "DESC", "states": [], "track_changes": 1}