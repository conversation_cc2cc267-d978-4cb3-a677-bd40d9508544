{"actions": [], "allow_rename": 1, "creation": "2023-11-30 00:25:17.085702", "doctype": "DocType", "editable_grid": 1, "engine": "InnoDB", "field_order": ["salary_slip", "employee", "transfer_currency", "transfer_amount", "beneficiary_bank_bic", "beneficiary_bank_sort_code", "beneficiary_bank_name", "beneficiary_bank_country_code", "beneficiary_name", "beneficiary_country", "beneficiary_address", "beneficiary_iban", "beneficiary_account_number", "beneficiary_account_type", "beneficiary_account_currency", "stanbic_finaud_status", "stanbic_intaud_status", "parent", "parentfield", "parenttype"], "fields": [{"fieldname": "salary_slip", "fieldtype": "Link", "label": "<PERSON><PERSON>", "options": "<PERSON><PERSON>"}, {"fetch_from": "salary_slip.employee", "fieldname": "employee", "fieldtype": "Link", "label": "Employee", "options": "Employee", "read_only": 1}, {"fetch_from": "salary_slip.currency", "fieldname": "transfer_currency", "fieldtype": "Data", "label": "Transfer currency", "read_only": 1}, {"fetch_from": "salary_slip.net_pay", "fieldname": "transfer_amount", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Transfer amount", "options": "transfer_currency"}, {"fetch_from": "employee.custom_beneficiary_bank_bic", "fieldname": "beneficiary_bank_bic", "fieldtype": "Data", "label": "Beneficiary Bank BIC", "read_only": 1}, {"fetch_from": "employee.bank_name", "fieldname": "beneficiary_bank_name", "fieldtype": "Data", "in_list_view": 1, "label": "Beneficiary bank name"}, {"fetch_from": "employee.bank_country_code", "fieldname": "beneficiary_bank_country_code", "fieldtype": "Data", "label": "Beneficiary bank country code", "read_only": 1}, {"fetch_from": "employee.employee_name", "fieldname": "beneficiary_name", "fieldtype": "Data", "in_list_view": 1, "label": "Beneficiary name"}, {"fetch_from": "employee.current_address", "fieldname": "beneficiary_address", "fieldtype": "Data", "label": "Beneficiary address", "read_only": 1}, {"fetch_from": "employee.iban", "fieldname": "beneficiary_iban", "fieldtype": "Data", "label": "Beneficiary IBAN"}, {"fetch_from": "employee.bank_ac_no", "fieldname": "beneficiary_account_number", "fieldtype": "Data", "in_list_view": 1, "label": "Beneficiary account number"}, {"default": "0", "fieldname": "beneficiary_account_type", "fieldtype": "Int", "label": "Beneficiary account type"}, {"fetch_from": "salary_slip.currency", "fieldname": "beneficiary_account_currency", "fieldtype": "Data", "label": "Beneficiary account currency", "read_only": 1}, {"fetch_from": "employee.bank_code", "fieldname": "beneficiary_bank_sort_code", "fieldtype": "Data", "label": "Beneficiary bank sort code"}, {"fetch_from": "employee.employee_country_code", "fieldname": "beneficiary_country", "fieldtype": "Data", "label": "Beneficiary Country"}, {"allow_on_submit": 1, "fieldname": "stanbic_intaud_status", "fieldtype": "Data", "in_list_view": 1, "label": "Stanbic INTAUD Status", "read_only": 1}, {"allow_on_submit": 1, "fieldname": "stanbic_finaud_status", "fieldtype": "Data", "in_list_view": 1, "label": "Stanbic FINAUD Status", "read_only": 1}, {"fieldname": "parent", "fieldtype": "Data", "hidden": 1, "label": "Parent", "no_copy": 1, "print_hide": 1, "read_only": 1}, {"fieldname": "parentfield", "fieldtype": "Data", "hidden": 1, "label": "Parent Field", "no_copy": 1, "print_hide": 1, "read_only": 1}, {"fieldname": "parenttype", "fieldtype": "Data", "hidden": 1, "label": "Parent Type", "no_copy": 1, "print_hide": 1, "read_only": 1}], "index_web_pages_for_search": 1, "istable": 1, "links": [], "modified": "2024-03-03 00:47:31.793117", "modified_by": "Administrator", "module": "<PERSON><PERSON>", "name": "Stanbic Payments Info", "owner": "Administrator", "permissions": [], "sort_field": "modified", "sort_order": "DESC", "states": []}