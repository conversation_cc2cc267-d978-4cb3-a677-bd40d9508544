{"actions": [], "creation": "2020-11-29 19:28:48.456126", "doctype": "DocType", "editable_grid": 1, "engine": "InnoDB", "field_order": ["invoice_type", "invoice_number", "invoice_date", "col_break1", "amount", "outstanding_amount", "currency", "parent", "parentfield", "parenttype"], "fields": [{"fieldname": "invoice_type", "fieldtype": "Select", "in_list_view": 1, "label": "Invoice Type", "options": "Sales Invoice\nPurchase Invoice\nJournal Entry", "read_only": 1}, {"fieldname": "invoice_number", "fieldtype": "Dynamic Link", "in_list_view": 1, "label": "Invoice Number", "options": "invoice_type", "read_only": 1}, {"fieldname": "invoice_date", "fieldtype": "Date", "in_list_view": 1, "label": "Invoice Date", "read_only": 1}, {"fieldname": "col_break1", "fieldtype": "Column Break"}, {"fieldname": "amount", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "in_list_view": 1, "label": "Amount", "options": "currency", "read_only": 1}, {"fieldname": "outstanding_amount", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "in_list_view": 1, "label": "Outstanding Amount", "options": "currency", "read_only": 1}, {"fieldname": "currency", "fieldtype": "Link", "hidden": 1, "label": "<PERSON><PERSON><PERSON><PERSON>", "options": "<PERSON><PERSON><PERSON><PERSON>"}, {"fieldname": "parent", "fieldtype": "Data", "hidden": 1, "label": "Parent", "no_copy": 1, "print_hide": 1, "read_only": 1}, {"fieldname": "parentfield", "fieldtype": "Data", "hidden": 1, "label": "Parent Field", "no_copy": 1, "print_hide": 1, "read_only": 1}, {"fieldname": "parenttype", "fieldtype": "Data", "hidden": 1, "label": "Parent Type", "no_copy": 1, "print_hide": 1, "read_only": 1}], "index_web_pages_for_search": 1, "istable": 1, "links": [], "modified": "2020-11-29 19:28:48.456126", "modified_by": "Administrator", "module": "CSF TZ", "name": "Payment Reconciliation Pro Invoice", "owner": "Administrator", "permissions": [], "quick_entry": 1, "sort_field": "modified", "sort_order": "DESC", "track_changes": 1}