{"actions": [], "allow_rename": 1, "creation": "2024-01-08 12:21:31.843374", "default_view": "List", "doctype": "DocType", "editable_grid": 1, "engine": "InnoDB", "field_order": ["item_sold_or_delivered", "rate_sold_or_delivered", "qty_sold_or_delivered", "amount_sold_or_delivered", "warehouse", "column_break_frc89", "item_exchange", "amount_exchange", "uom", "parent", "parentfield", "parenttype"], "fields": [{"fieldname": "item_sold_or_delivered", "fieldtype": "Link", "in_list_view": 1, "label": "Item Sold/Delivered", "options": "<PERSON><PERSON>", "read_only": 1}, {"fieldname": "rate_sold_or_delivered", "fieldtype": "Data", "label": "Rate Sold/Delivered", "read_only": 1}, {"fieldname": "qty_sold_or_delivered", "fieldtype": "Data", "label": "Qty Sold/Delivered", "read_only": 1}, {"fieldname": "amount_sold_or_delivered", "fieldtype": "Data", "in_list_view": 1, "label": "Amount Sold/Delivered ", "read_only": 1}, {"fieldname": "warehouse", "fieldtype": "Link", "label": "Warehouse", "options": "Warehouse", "read_only": 1}, {"fieldname": "column_break_frc89", "fieldtype": "Column Break"}, {"fieldname": "item_exchange", "fieldtype": "Link", "in_list_view": 1, "label": "Item Exchange", "options": "<PERSON><PERSON>", "reqd": 1}, {"fieldname": "amount_exchange", "fieldtype": "Data", "in_list_view": 1, "label": "Amount Exchange", "read_only": 1}, {"fieldname": "uom", "fieldtype": "Link", "label": "UOM", "options": "UOM", "read_only": 1}, {"fieldname": "parent", "fieldtype": "Data", "hidden": 1, "label": "Parent", "no_copy": 1, "print_hide": 1, "read_only": 1}, {"fieldname": "parentfield", "fieldtype": "Data", "hidden": 1, "label": "Parent Field", "no_copy": 1, "print_hide": 1, "read_only": 1}, {"fieldname": "parenttype", "fieldtype": "Data", "hidden": 1, "label": "Parent Type", "no_copy": 1, "print_hide": 1, "read_only": 1}], "index_web_pages_for_search": 1, "istable": 1, "links": [], "modified": "2024-01-08 15:47:56.471503", "modified_by": "Administrator", "module": "CSF TZ", "name": "Delivery Exchange Non Stock Item Details", "owner": "Administrator", "permissions": [], "sort_field": "modified", "sort_order": "DESC", "states": []}