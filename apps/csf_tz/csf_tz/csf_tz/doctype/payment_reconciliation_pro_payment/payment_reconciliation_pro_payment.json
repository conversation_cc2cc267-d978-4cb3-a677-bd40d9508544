{"actions": [], "creation": "2020-11-29 19:28:24.126126", "doctype": "DocType", "editable_grid": 1, "engine": "InnoDB", "field_order": ["reference_type", "reference_name", "posting_date", "is_advance", "reference_row", "col_break1", "invoice_number", "amount", "allocated_amount", "section_break_10", "difference_account", "difference_amount", "sec_break1", "remark", "currency", "parent", "parentfield", "parenttype"], "fields": [{"fieldname": "reference_type", "fieldtype": "Link", "label": "Reference Type", "options": "DocType", "read_only": 1}, {"columns": 2, "fieldname": "reference_name", "fieldtype": "Dynamic Link", "in_list_view": 1, "label": "Reference Name", "options": "reference_type", "read_only": 1}, {"fieldname": "posting_date", "fieldtype": "Date", "label": "Posting Date", "read_only": 1}, {"fieldname": "is_advance", "fieldtype": "Data", "hidden": 1, "label": "Is Advance", "read_only": 1}, {"fieldname": "reference_row", "fieldtype": "Data", "hidden": 1, "label": "Reference Row", "read_only": 1}, {"fieldname": "col_break1", "fieldtype": "Column Break"}, {"columns": 2, "fieldname": "invoice_number", "fieldtype": "Select", "in_list_view": 1, "label": "Invoice Number", "reqd": 1}, {"columns": 2, "fieldname": "amount", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "in_list_view": 1, "label": "Amount", "options": "currency", "read_only": 1}, {"columns": 2, "fieldname": "allocated_amount", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "in_list_view": 1, "label": "Allocated amount", "options": "currency", "reqd": 1}, {"fieldname": "section_break_10", "fieldtype": "Section Break"}, {"columns": 2, "fieldname": "difference_account", "fieldtype": "Link", "in_list_view": 1, "label": "Difference Account", "options": "Account"}, {"fieldname": "difference_amount", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Difference Amount", "options": "currency", "print_hide": 1, "read_only": 1}, {"fieldname": "sec_break1", "fieldtype": "Section Break"}, {"fieldname": "remark", "fieldtype": "Small Text", "in_list_view": 1, "label": "Remark", "read_only": 1}, {"fieldname": "currency", "fieldtype": "Link", "hidden": 1, "label": "<PERSON><PERSON><PERSON><PERSON>", "options": "<PERSON><PERSON><PERSON><PERSON>"}, {"fieldname": "parent", "fieldtype": "Data", "hidden": 1, "label": "Parent", "no_copy": 1, "print_hide": 1, "read_only": 1}, {"fieldname": "parentfield", "fieldtype": "Data", "hidden": 1, "label": "Parent Field", "no_copy": 1, "print_hide": 1, "read_only": 1}, {"fieldname": "parenttype", "fieldtype": "Data", "hidden": 1, "label": "Parent Type", "no_copy": 1, "print_hide": 1, "read_only": 1}], "index_web_pages_for_search": 1, "istable": 1, "links": [], "modified": "2020-11-29 19:29:19.319027", "modified_by": "Administrator", "module": "CSF TZ", "name": "Payment Reconciliation Pro Payment", "owner": "Administrator", "permissions": [], "quick_entry": 1, "sort_field": "modified", "sort_order": "DESC"}