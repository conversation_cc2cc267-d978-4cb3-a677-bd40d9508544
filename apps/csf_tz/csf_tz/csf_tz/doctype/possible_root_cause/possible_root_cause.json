{"actions": [], "allow_rename": 1, "creation": "2022-05-28 12:55:52.861403", "doctype": "DocType", "editable_grid": 1, "engine": "InnoDB", "field_order": ["root_cause_of_the_issue", "rate_probability", "description", "category_column_break", "categorize_root_cause", "please_specify", "investigation_section_break", "investigation", "investigation_area", "interview_conducted", "interview_summary", "interview_column_break", "number_of_people_interviewed", "name_of_interviewer", "findings_and_evidences_section_break", "findings_of_investigation", "evidence_available", "list_evidence_column_break", "list_documented_evidence", "attach_evidence", "parent", "parentfield", "parenttype"], "fields": [{"fieldname": "root_cause_of_the_issue", "fieldtype": "Data", "in_list_view": 1, "label": "What is the root cause of the issue?", "reqd": 1}, {"fieldname": "rate_probability", "fieldtype": "Select", "in_list_view": 1, "label": "Rate probability as the root cause", "options": "\nLow\nMedium\nHigh"}, {"fieldname": "category_column_break", "fieldtype": "Column Break"}, {"fieldname": "categorize_root_cause", "fieldtype": "Select", "in_list_view": 1, "label": "Categorize the root cause", "options": "\nProcess\nEquipment\nLack of Communication\nLack of Knowledge\nIncorrect Information\n3rd Party Software\nNetwork\nOther", "reqd": 1}, {"depends_on": "eval: doc.categorize_root_cause == \"Other\"", "fieldname": "please_specify", "fieldtype": "Small Text", "label": "Please specify", "mandatory_depends_on": "eval: doc.categorize_root_cause == \"Other\""}, {"description": "Brief describle the root cause depending on the category of the rout cause", "fieldname": "description", "fieldtype": "Small Text", "label": "Description"}, {"fieldname": "investigation", "fieldtype": "Select", "label": "Were any Investigation done?", "options": "\nNo\nYes"}, {"depends_on": "eval: doc.investigation == \"Yes\"\r\n", "fieldname": "investigation_area", "fieldtype": "Data", "label": "In which area/department did the investigation begin?", "mandatory_depends_on": "eval: doc.investigation == \"Yes\"\r\n"}, {"depends_on": "eval: doc.interview_conducted == \"Yes\"\r\n", "fieldname": "number_of_people_interviewed", "fieldtype": "Data", "label": "How Many People Interviewed", "mandatory_depends_on": "eval: doc.interview_conducted == \"Yes\"\r\n"}, {"depends_on": "eval: doc.interview_conducted == \"Yes\"\r\n", "fieldname": "name_of_interviewer", "fieldtype": "Small Text", "label": "Name(s) of Interviewers", "mandatory_depends_on": "eval: doc.interview_conducted == \"Yes\"\r\n"}, {"depends_on": "eval: doc.interview_conducted == \"Yes\"\r\n", "fieldname": "interview_summary", "fieldtype": "Small Text", "label": "Give a brief summary of the interview.", "mandatory_depends_on": "eval: doc.interview_conducted == \"Yes\"\r\n"}, {"fieldname": "findings_of_investigation", "fieldtype": "Small Text", "label": "List any findings pertinent to the investigation."}, {"fieldname": "evidence_available", "fieldtype": "Select", "label": "Was any evidence available that helped determine the root cause?", "options": "\nNo\nYes"}, {"depends_on": "eval: doc.evidence_available == \"Yes\"\r\n", "fieldname": "list_documented_evidence", "fieldtype": "Small Text", "label": "List the items documented as evidence of the root cause.", "mandatory_depends_on": "eval: doc.evidence_available == \"Yes\"\r\n"}, {"depends_on": "eval: doc.evidence_available == \"Yes\"\r\n", "fieldname": "attach_evidence", "fieldtype": "Attach", "label": "Attach Evidence", "mandatory_depends_on": "eval: doc.evidence_available == \"Yes\"\r\n"}, {"fieldname": "investigation_section_break", "fieldtype": "Section Break", "label": "Investigation"}, {"fieldname": "interview_conducted", "fieldtype": "Select", "label": "Were any interviews conducted?", "options": "\nNo\nYes"}, {"fieldname": "findings_and_evidences_section_break", "fieldtype": "Section Break", "label": "Findings and Evidences"}, {"fieldname": "list_evidence_column_break", "fieldtype": "Column Break"}, {"fieldname": "interview_column_break", "fieldtype": "Column Break"}, {"fieldname": "parent", "fieldtype": "Data", "hidden": 1, "label": "Parent", "no_copy": 1, "print_hide": 1, "read_only": 1}, {"fieldname": "parentfield", "fieldtype": "Data", "hidden": 1, "label": "Parent Field", "no_copy": 1, "print_hide": 1, "read_only": 1}, {"fieldname": "parenttype", "fieldtype": "Data", "hidden": 1, "label": "Parent Type", "no_copy": 1, "print_hide": 1, "read_only": 1}], "index_web_pages_for_search": 1, "istable": 1, "links": [], "modified": "2022-06-01 18:05:58.567278", "modified_by": "Administrator", "module": "CSF TZ", "name": "Possible Root Cause", "owner": "Administrator", "permissions": [], "sort_field": "modified", "sort_order": "DESC"}